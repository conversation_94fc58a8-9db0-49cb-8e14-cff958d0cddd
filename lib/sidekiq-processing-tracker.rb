# frozen_string_literal: true

require "sidekiq"
require "redis"
require "logger"
require "securerandom"
require "set"

require_relative "sidekiq/processing_tracker/version"
require_relative "sidekiq/processing_tracker/middleware"
require_relative "sidekiq/processing_tracker/worker"

module Sidekiq
  module ProcessingTracker
    class Error < StandardError; end

    class << self
      attr_accessor :instance_id, :namespace, :heartbeat_interval, :heartbeat_ttl, :recovery_lock_ttl, :recovery_interval, :logger, :redis_options

      def configure
        yield self if block_given?
        setup_defaults
        setup_sidekiq_hooks
      end

      def redis(&block)
        if redis_options
          # Use custom Redis configuration if provided
          redis_client = Redis.new(redis_options)
          if block_given?
            result = yield redis_client
            redis_client.close
            result
          else
            redis_client
          end
        else
          # Use Sidekiq's Redis connection pool
          Sidekiq.redis(&block)
        end
      end

      def redis_sync(&block)
        # Synchronous Redis operations using Sidekiq's pool or custom config
        redis(&block)
      end

      # Helper method to add namespace prefix to Redis keys
      def namespaced_key(key)
        "#{namespace}:#{key}"
      end

      # Clear unique-jobs lock for orphaned jobs to allow immediate re-enqueuing
      def clear_unique_jobs_lock(job_data)
        return unless job_data['unique_digest']

        begin
          # Check if SidekiqUniqueJobs is available
          if defined?(SidekiqUniqueJobs::Digests)
            SidekiqUniqueJobs::Digests.del(digest: job_data['unique_digest'])
            logger.info "ProcessingTracker cleared unique-jobs lock for job #{job_data['jid']} with digest #{job_data['unique_digest']}"
          else
            logger.info "ProcessingTracker: SidekiqUniqueJobs not available, skipping lock cleanup for job #{job_data['jid']}"
          end
        rescue => e
          logger.warn "ProcessingTracker failed to clear unique-jobs lock for job #{job_data['jid']}: #{e.message}"
          logger.warn e.backtrace.join("\n")
        end
      end

      def reenqueue_orphans!
        with_recovery_lock do
          logger.info "ProcessingTracker starting orphan job recovery"

          redis_sync do |conn|
            # Get all job keys and instance keys (using custom namespacing)
            job_keys = conn.keys(namespaced_key("jobs:*"))
            instance_keys = conn.keys(namespaced_key("instance:*"))

            # Extract instance IDs from keys
            live_instances = instance_keys.map { |key| key.split(":").last }.to_set

            orphaned_jobs = []

            job_keys.each do |job_key|
              instance_id = job_key.split(":").last
              unless live_instances.include?(instance_id)
                # Get all job IDs for this dead instance
                job_ids = conn.smembers(job_key)

                job_ids.each do |jid|
                  # Get the job payload
                  job_data_key = namespaced_key("job:#{jid}")
                  job_payload = conn.get(job_data_key)

                  if job_payload
                    orphaned_jobs << JSON.parse(job_payload)
                    # Clean up the job data key
                    conn.del(job_data_key)
                  end
                end

                # Clean up the job tracking key
                conn.del(job_key)
              end
            end

            if orphaned_jobs.any?
              logger.info "ProcessingTracker found #{orphaned_jobs.size} orphaned jobs, re-enqueuing"
              orphaned_jobs.each do |job_data|
                # Clear unique-jobs lock before re-enqueuing to avoid lock conflicts
                clear_unique_jobs_lock(job_data)

                Sidekiq::Client.push(job_data)
                logger.info "ProcessingTracker re-enqueued job #{job_data['jid']} (#{job_data['class']})"
              end
            else
              logger.info "ProcessingTracker found no orphaned jobs"
            end
          end
        end
      rescue => e
        logger.error "ProcessingTracker orphan recovery failed: #{e.message}"
        logger.error e.backtrace.join("\n")
      end

      def setup_sidekiq_hooks
        return unless defined?(Sidekiq::VERSION)

        Sidekiq.configure_server do |config|
          config.server_middleware do |chain|
            chain.add ProcessingTracker::Middleware
          end

          # Add startup hook for heartbeat and orphan recovery
          config.on(:startup) do
            # Ensure configuration is set up
            setup_defaults unless @instance_id

            logger.info "ProcessingTracker starting up on instance #{instance_id}"

            # Start heartbeat system
            setup_heartbeat

            # Start periodic orphan recovery system
            setup_periodic_recovery
          end

          # Add shutdown hook to clean up
          config.on(:shutdown) do
            logger.info "ProcessingTracker shutting down instance #{instance_id}"
            begin
              # Stop background threads
              if @heartbeat_thread&.alive?
                @heartbeat_thread.kill
                @heartbeat_thread = nil
              end

              if @recovery_thread&.alive?
                @recovery_thread.kill
                @recovery_thread = nil
              end

              redis_sync do |conn|
                # Only clean up instance heartbeat - let orphan recovery handle job cleanup
                # This ensures that if there are running jobs during shutdown, they will be
                # detected as orphaned and recovered by the next instance
                conn.del(namespaced_key("instance:#{instance_id}"))

                # Log tracked jobs but don't clean them up - they should be recovered as orphans
                job_tracking_key = namespaced_key("jobs:#{instance_id}")
                tracked_jobs = conn.smembers(job_tracking_key)

                if tracked_jobs.any?
                  logger.warn "ProcessingTracker leaving #{tracked_jobs.size} tracked jobs for orphan recovery: #{tracked_jobs.join(', ')}"
                  logger.info "ProcessingTracker: These jobs will be recovered by the next instance startup"
                else
                  logger.info "ProcessingTracker: No tracked jobs to leave for recovery"
                end
              end
            rescue => e
              logger.error "ProcessingTracker shutdown cleanup failed: #{e.message}"
            end
          end
        end
      end

      private

      def setup_defaults
        @instance_id ||= ENV.fetch("PROCESSING_INSTANCE_ID") { SecureRandom.hex(8) }
        @namespace ||= ENV.fetch("PROCESSING_NS", "sidekiq_processing")
        @heartbeat_interval ||= ENV.fetch("HEARTBEAT_INTERVAL", "15").to_i  # Faster heartbeat
        @heartbeat_ttl ||= ENV.fetch("HEARTBEAT_TTL", "45").to_i            # Shorter TTL
        @recovery_lock_ttl ||= ENV.fetch("RECOVERY_LOCK_TTL", "300").to_i
        @recovery_interval ||= ENV.fetch("RECOVERY_INTERVAL", "60").to_i    # Periodic recovery
        @logger ||= Sidekiq.logger
      end

      def setup_heartbeat
        # Initial heartbeat
        send_heartbeat

        # Background heartbeat thread
        @heartbeat_thread = Thread.new do
          loop do
            sleep heartbeat_interval
            begin
              send_heartbeat
            rescue => e
              logger.error "ProcessingTracker heartbeat failed: #{e.message}"
            end
          end
        end
      end

      def setup_periodic_recovery
        # Run initial recovery after a short delay
        Thread.new do
          sleep 5 # Give the server a moment to fully start
          begin
            reenqueue_orphans!
          rescue => e
            logger.error "ProcessingTracker startup orphan recovery failed: #{e.message}"
            logger.error e.backtrace.join("\n")
          end
        end

        # Background periodic recovery thread
        @recovery_thread = Thread.new do
          loop do
            sleep recovery_interval
            begin
              reenqueue_orphans!
            rescue => e
              logger.error "ProcessingTracker periodic orphan recovery failed: #{e.message}"
              logger.error e.backtrace.join("\n")
            end
          end
        end
      end



      def send_heartbeat
        key = namespaced_key("instance:#{instance_id}")
        redis_sync do |conn|
          conn.setex(key, heartbeat_ttl, Time.now.to_f)
        end
        logger.debug "ProcessingTracker heartbeat sent for instance #{instance_id}"
      end

      def with_recovery_lock
        lock_key = namespaced_key("recovery_lock")
        lock_acquired = redis_sync do |conn|
          conn.set(lock_key, instance_id, nx: true, ex: recovery_lock_ttl)
        end

        if lock_acquired
          logger.info "ProcessingTracker recovery lock acquired by instance #{instance_id}"
          begin
            yield
          ensure
            redis_sync { |conn| conn.del(lock_key) }
            logger.info "ProcessingTracker recovery lock released by instance #{instance_id}"
          end
        else
          logger.debug "ProcessingTracker recovery lock not acquired, another instance is handling recovery"
        end
      end


    end
  end
end

# Auto-setup defaults and Sidekiq hooks when gem is required
Sidekiq::ProcessingTracker.send(:setup_defaults)
Sidekiq::ProcessingTracker.setup_sidekiq_hooks
