# Shutdown Hook Fix - Orphan Job Recovery Issue

## 🐛 **Problem Description**

### **Issue**
When Sidekiq containers were stopped (e.g., during restarts), the ProcessingTracker's shutdown hook was too aggressive and cleaned up all job tracking data, including data for jobs that were still running. This caused orphaned jobs to be lost instead of being recovered.

### **Symptoms**
- Jobs running during container shutdown were lost
- Orphan recovery on restart found "no orphaned jobs"
- Long-running jobs (like IdentityPhotoGenerationWorker with 100s sleep) were not recovered
- Logs showed: `ProcessingTracker found no orphaned jobs`

### **Root Cause**
The shutdown hook in `lib/sidekiq-processing-tracker.rb` was cleaning up:
1. Instance heartbeat (✅ correct)
2. Job tracking sets (❌ problematic)
3. Individual job data (❌ problematic)

This meant that when a new instance started, there was no orphaned data to recover.

## 🔧 **Solution**

### **Fix Applied**
Modified the shutdown hook to **only** remove the instance heartbeat, leaving job tracking data intact for orphan recovery.

### **Before (Problematic Code)**
```ruby
redis_sync do |conn|
  # Clean up instance heartbeat
  conn.del(namespaced_key("instance:#{instance_id}"))

  # Clean up any remaining job tracking for this instance
  job_tracking_key = namespaced_key("jobs:#{instance_id}")
  tracked_jobs = conn.smembers(job_tracking_key)

  if tracked_jobs.any?
    logger.warn "ProcessingTracker cleaning up #{tracked_jobs.size} tracked jobs on shutdown"
    tracked_jobs.each do |jid|
      conn.del(namespaced_key("job:#{jid}"))  # ❌ Deletes job data
    end
    conn.del(job_tracking_key)  # ❌ Deletes tracking set
  end
end
```

### **After (Fixed Code)**
```ruby
redis_sync do |conn|
  # Only clean up instance heartbeat - let orphan recovery handle job cleanup
  # This ensures that if there are running jobs during shutdown, they will be
  # detected as orphaned and recovered by the next instance
  conn.del(namespaced_key("instance:#{instance_id}"))
  
  # Log tracked jobs but don't clean them up - they should be recovered as orphans
  job_tracking_key = namespaced_key("jobs:#{instance_id}")
  tracked_jobs = conn.smembers(job_tracking_key)
  
  if tracked_jobs.any?
    logger.warn "ProcessingTracker leaving #{tracked_jobs.size} tracked jobs for orphan recovery: #{tracked_jobs.join(', ')}"
    logger.info "ProcessingTracker: These jobs will be recovered by the next instance startup"
  else
    logger.info "ProcessingTracker: No tracked jobs to leave for recovery"
  end
end
```

## ✅ **How It Works Now**

### **Shutdown Process**
1. **Stop heartbeat thread** - Prevents new heartbeats
2. **Remove instance heartbeat** - Marks instance as "dead"
3. **Leave job tracking data** - Allows orphan recovery to find jobs
4. **Log what's left** - Shows which jobs will be recovered

### **Startup Process**
1. **New instance starts** - Gets new instance ID
2. **Send heartbeat** - Marks new instance as "live"
3. **Run orphan recovery** - Scans for jobs from dead instances
4. **Detect orphans** - Finds jobs from instances without heartbeats
5. **Re-enqueue jobs** - Puts orphaned jobs back in Sidekiq queues
6. **Clean up** - Removes orphaned tracking data

## 🧪 **Testing**

### **Test Results**
- ✅ All existing tests pass
- ✅ Orphan recovery works correctly
- ✅ Jobs are re-enqueued with original data
- ✅ Unique job locks are cleared (when SidekiqUniqueJobs available)

### **Scenario Validation**
Tested with the exact scenario from the issue:
- **Job**: IdentityPhotoGenerationWorker with 100s sleep
- **Instance**: `a35a908a42479259` (from logs)
- **Job ID**: `5a5daa582ce26bc26d9c06d4` (from logs)
- **Result**: ✅ Job successfully recovered and re-enqueued

## 📊 **Impact**

### **Benefits**
- **No more lost jobs** during container restarts
- **Immediate recovery** on startup (no waiting for timeouts)
- **Preserves job data** including arguments and metadata
- **Works with unique jobs** (clears locks before re-enqueuing)

### **Behavior Changes**
- **Shutdown logs** now show jobs being "left for recovery" instead of "cleaned up"
- **Startup logs** will show orphan recovery finding and re-enqueuing jobs
- **Redis data** persists longer (until next instance recovers it)

## 🔍 **Debugging**

### **Check Redis State**
Use the provided debug script:
```bash
ruby debug_redis_state.rb
```

### **Expected Logs After Fix**

**During Shutdown:**
```
ProcessingTracker shutting down instance a35a908a42479259
ProcessingTracker leaving 1 tracked jobs for orphan recovery: 5a5daa582ce26bc26d9c06d4
ProcessingTracker: These jobs will be recovered by the next instance startup
```

**During Startup:**
```
ProcessingTracker starting up on instance 394469022c22e563
ProcessingTracker recovery lock acquired by instance 394469022c22e563
ProcessingTracker starting orphan job recovery
ProcessingTracker found 1 orphaned jobs, re-enqueuing
ProcessingTracker re-enqueued job 5a5daa582ce26bc26d9c06d4 (IdentityPhotoGenerationWorker)
ProcessingTracker recovery lock released by instance 394469022c22e563
```

## 🚀 **Deployment**

This fix is backward compatible and can be deployed immediately. No configuration changes required.

### **Verification Steps**
1. Deploy the updated gem
2. Restart Sidekiq containers
3. Check logs for "leaving X tracked jobs for orphan recovery"
4. Verify orphan recovery finds and re-enqueues jobs on startup
