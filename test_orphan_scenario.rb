#!/usr/bin/env ruby

# Test script to simulate the orphan job scenario
require 'sidekiq'
require 'redis'
require 'json'
require_relative 'lib/sidekiq-processing-tracker'

# Configure Sidekiq
Sidekiq.configure_client do |config|
  config.redis = { url: ENV['REDIS_URL'] || 'redis://localhost:6379' }
end

Sidekiq.configure_server do |config|
  config.redis = { url: ENV['REDIS_URL'] || 'redis://localhost:6379' }
end

# Test worker that includes ProcessingTracker
class TestWorker
  include Sidekiq::Worker
  include Sidekiq::ProcessingTracker::Worker
  
  def perform(message, sleep_time = 5)
    puts "TestWorker starting: #{message}"
    puts "Job ID: #{jid}"
    puts "Sleeping for #{sleep_time} seconds..."
    sleep(sleep_time)
    puts "TestWorker completed: #{message}"
  end
end

def simulate_orphan_scenario
  puts "=== Simulating Orphan Job Scenario ==="
  puts
  
  # Step 1: Create a fake "dead" instance with orphaned jobs
  puts "Step 1: Creating orphaned job data for a 'dead' instance..."
  
  dead_instance_id = "dead_instance_#{Time.now.to_i}"
  job_id = "test_job_#{Time.now.to_i}"
  
  job_data = {
    "class" => "TestWorker",
    "args" => ["Orphaned job test", 2],
    "jid" => job_id,
    "queue" => "default",
    "created_at" => Time.now.to_f,
    "retry" => true
  }
  
  # Manually create the orphaned job state in Redis
  Sidekiq::ProcessingTracker.redis_sync do |conn|
    # Add job to tracking set for dead instance
    job_tracking_key = Sidekiq::ProcessingTracker.send(:namespaced_key, "jobs:#{dead_instance_id}")
    job_data_key = Sidekiq::ProcessingTracker.send(:namespaced_key, "job:#{job_id}")
    
    conn.sadd(job_tracking_key, job_id)
    conn.set(job_data_key, job_data.to_json)
    
    puts "✅ Created orphaned job tracking:"
    puts "   Instance: #{dead_instance_id}"
    puts "   Job ID: #{job_id}"
    puts "   Tracking key: #{job_tracking_key}"
    puts "   Data key: #{job_data_key}"
  end
  
  puts
  
  # Step 2: Check current Redis state
  puts "Step 2: Current Redis state before recovery..."
  system("ruby debug_redis_state.rb")
  puts
  
  # Step 3: Trigger orphan recovery manually
  puts "Step 3: Triggering orphan recovery..."
  
  # Configure ProcessingTracker for this test
  Sidekiq::ProcessingTracker.configure do |config|
    config.instance_id = "recovery_test_#{Time.now.to_i}"
    config.namespace = "sidekiq_processing"
    config.heartbeat_interval = 30
    config.heartbeat_ttl = 90
    config.recovery_lock_ttl = 300
  end
  
  # Send a heartbeat so this instance is considered "live"
  Sidekiq::ProcessingTracker.send(:send_heartbeat)
  
  puts "Recovery instance ID: #{Sidekiq::ProcessingTracker.instance_id}"
  
  # Run orphan recovery
  begin
    Sidekiq::ProcessingTracker.reenqueue_orphans!
    puts "✅ Orphan recovery completed"
  rescue => e
    puts "❌ Orphan recovery failed: #{e.message}"
    puts e.backtrace.join("\n")
  end
  
  puts
  
  # Step 4: Check Redis state after recovery
  puts "Step 4: Redis state after recovery..."
  system("ruby debug_redis_state.rb")
  puts
  
  # Step 5: Check Sidekiq queues
  puts "Step 5: Checking Sidekiq queues..."
  Sidekiq.redis do |conn|
    default_queue_size = conn.llen("queue:default")
    puts "Default queue size: #{default_queue_size}"
    
    if default_queue_size > 0
      puts "✅ Job was successfully re-enqueued!"
      
      # Peek at the job
      job_json = conn.lindex("queue:default", -1)
      if job_json
        job = JSON.parse(job_json)
        puts "Re-enqueued job: #{job['class']} with args #{job['args']}"
      end
    else
      puts "❌ No jobs found in default queue"
    end
  end
  
  puts
  puts "=== Test completed ==="
end

# Run the simulation
if __FILE__ == $0
  simulate_orphan_scenario
end
