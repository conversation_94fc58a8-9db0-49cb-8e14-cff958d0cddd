#!/usr/bin/env ruby

# Debug script to inspect ProcessingTracker Redis state
require 'redis'
require 'json'
require 'set'

# Configuration - adjust these to match your setup
REDIS_URL = ENV['REDIS_URL'] || 'redis://localhost:6379'
NAMESPACE = ENV['PROCESSING_NS'] || 'sidekiq_processing'

def namespaced_key(key)
  "#{NAMESPACE}:#{key}"
end

def debug_redis_state
  redis = Redis.new(url: REDIS_URL)
  
  puts "=== ProcessingTracker Redis State Debug ==="
  puts "Redis URL: #{REDIS_URL}"
  puts "Namespace: #{NAMESPACE}"
  puts "Time: #{Time.now}"
  puts

  # Check instance heartbeats
  puts "=== Instance Heartbeats ==="
  instance_keys = redis.keys(namespaced_key("instance:*"))
  if instance_keys.empty?
    puts "❌ No live instances found"
  else
    instance_keys.each do |key|
      instance_id = key.split(":").last
      timestamp = redis.get(key)
      ttl = redis.ttl(key)
      time_ago = Time.now.to_f - timestamp.to_f
      
      puts "✅ Instance: #{instance_id}"
      puts "   Heartbeat: #{Time.at(timestamp.to_f)} (#{time_ago.round(1)}s ago)"
      puts "   TTL: #{ttl}s"
      puts
    end
  end

  # Check job tracking sets
  puts "=== Job Tracking Sets ==="
  job_keys = redis.keys(namespaced_key("jobs:*"))
  if job_keys.empty?
    puts "❌ No job tracking sets found"
  else
    job_keys.each do |key|
      instance_id = key.split(":").last
      job_count = redis.scard(key)
      job_ids = redis.smembers(key)
      
      puts "📋 Instance: #{instance_id}"
      puts "   Tracked jobs: #{job_count}"
      puts "   Job IDs: #{job_ids.join(', ')}" if job_ids.any?
      puts
    end
  end

  # Check individual job data
  puts "=== Individual Job Data ==="
  job_data_keys = redis.keys(namespaced_key("job:*"))
  if job_data_keys.empty?
    puts "❌ No job data found"
  else
    job_data_keys.each do |key|
      jid = key.split(":").last
      job_data = redis.get(key)
      
      if job_data
        parsed = JSON.parse(job_data)
        puts "💼 Job: #{jid}"
        puts "   Class: #{parsed['class']}"
        puts "   Queue: #{parsed['queue']}"
        puts "   Args: #{parsed['args']}"
        puts "   Created: #{Time.at(parsed['created_at'] || 0)}" if parsed['created_at']
        puts
      end
    end
  end

  # Check recovery lock
  puts "=== Recovery Lock ==="
  lock_key = namespaced_key("recovery_lock")
  lock_value = redis.get(lock_key)
  if lock_value
    lock_ttl = redis.ttl(lock_key)
    puts "🔒 Recovery lock held by: #{lock_value}"
    puts "   TTL: #{lock_ttl}s"
  else
    puts "🔓 No recovery lock active"
  end
  puts

  # Summary for orphan detection
  puts "=== Orphan Detection Summary ==="
  live_instances = instance_keys.map { |key| key.split(":").last }.to_set
  
  orphaned_count = 0
  job_keys.each do |job_key|
    instance_id = job_key.split(":").last
    unless live_instances.include?(instance_id)
      job_count = redis.scard(job_key)
      orphaned_count += job_count
      puts "💀 Dead instance #{instance_id} has #{job_count} orphaned jobs"
    end
  end
  
  if orphaned_count == 0
    puts "✅ No orphaned jobs detected"
  else
    puts "⚠️  Total orphaned jobs: #{orphaned_count}"
  end

rescue => e
  puts "❌ Error connecting to Redis: #{e.message}"
  puts "   Make sure Redis is running and REDIS_URL is correct"
end

# Run the debug
debug_redis_state
