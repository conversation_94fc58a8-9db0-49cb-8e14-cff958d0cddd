#!/usr/bin/env ruby
# frozen_string_literal: true

# Test script to verify unique jobs lock clearing is working
require_relative "lib/sidekiq-processing-tracker"

puts "=== Testing Unique Jobs Lock Clearing ==="

# Test job data with unique_digest
job_data_with_unique = {
  'jid' => 'test_unique_123',
  'class' => 'UniqueWorker',
  'args' => [123],
  'unique_digest' => 'unique_digest_456',
  'queue' => 'default'
}

# Test job data without unique_digest
job_data_without_unique = {
  'jid' => 'test_regular_456',
  'class' => 'RegularWorker',
  'args' => [456],
  'queue' => 'default'
}

puts "Testing job with unique_digest:"
puts "Job data: #{job_data_with_unique.inspect}"

# Test the clear_unique_jobs_lock method
begin
  Sidekiq::ProcessingTracker.send(:clear_unique_jobs_lock, job_data_with_unique)
  puts "✅ clear_unique_jobs_lock completed for unique job"
rescue => e
  puts "❌ Error clearing unique job lock: #{e.message}"
end

puts "\nTesting job without unique_digest:"
puts "Job data: #{job_data_without_unique.inspect}"

begin
  Sidekiq::ProcessingTracker.send(:clear_unique_jobs_lock, job_data_without_unique)
  puts "✅ clear_unique_jobs_lock completed for regular job (should be skipped)"
rescue => e
  puts "❌ Error with regular job: #{e.message}"
end

puts "\n=== Testing SidekiqUniqueJobs Detection ==="

if defined?(SidekiqUniqueJobs::Digests)
  puts "✅ SidekiqUniqueJobs is available"
  puts "SidekiqUniqueJobs::Digests class: #{SidekiqUniqueJobs::Digests}"
else
  puts "ℹ️  SidekiqUniqueJobs is not available (this is normal if not installed)"
end

puts "\n=== Testing Orphan Recovery Process ==="

# Test the full orphan recovery process
puts "Testing reenqueue_orphans! method..."

begin
  # This will test the full recovery process including unique jobs handling
  Sidekiq::ProcessingTracker.reenqueue_orphans!
  puts "✅ reenqueue_orphans! completed successfully"
rescue => e
  puts "❌ Error in reenqueue_orphans!: #{e.message}"
  puts e.backtrace.join("\n")
end

puts "\n=== Configuration Info ==="
puts "Instance ID: #{Sidekiq::ProcessingTracker.instance_id}"
puts "Namespace: #{Sidekiq::ProcessingTracker.namespace}"
puts "Heartbeat interval: #{Sidekiq::ProcessingTracker.heartbeat_interval}"
puts "Heartbeat TTL: #{Sidekiq::ProcessingTracker.heartbeat_ttl}"
puts "Recovery lock TTL: #{Sidekiq::ProcessingTracker.recovery_lock_ttl}"

puts "\n=== Test Complete ==="
