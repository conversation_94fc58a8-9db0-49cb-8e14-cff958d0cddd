#!/usr/bin/env ruby

# Test script to simulate your IdentityPhotoGenerationWorker scenario
require 'sidekiq'
require 'redis'
require 'json'
require_relative 'lib/sidekiq-processing-tracker'

# Configure Sidekiq
Sidekiq.configure_client do |config|
  config.redis = { url: ENV['REDIS_URL'] || 'redis://localhost:6379' }
end

Sidekiq.configure_server do |config|
  config.redis = { url: ENV['REDIS_URL'] || 'redis://localhost:6379' }
end

# Simulate your IdentityPhotoGenerationWorker
class IdentityPhotoGenerationWorker
  include Sidekiq::Worker
  include Sidekiq::ProcessingTracker::Worker
  
  def perform(user_id, options = {})
    puts "IdentityPhotoGenerationWorker starting for user #{user_id}"
    puts "Job ID: #{jid}"
    puts "Options: #{options}"
    puts "Sleeping for 100 seconds to simulate long processing..."
    sleep(100)  # Your worker sleeps for 100 seconds
    puts "IdentityPhotoGenerationWorker completed for user #{user_id}"
  end
end

def simulate_your_scenario
  puts "=== Simulating Your IdentityPhotoGenerationWorker Scenario ==="
  puts
  
  # Step 1: Create the exact job that was running when you stopped the container
  puts "Step 1: Creating the job that was running when container stopped..."
  
  # Use the instance ID from your logs
  dead_instance_id = "a35a908a42479259"  # From your log
  job_id = "5a5daa582ce26bc26d9c06d4"      # From your log
  
  job_data = {
    "class" => "IdentityPhotoGenerationWorker",
    "args" => [12345, {"some" => "options"}],
    "jid" => job_id,
    "queue" => "default",
    "created_at" => Time.now.to_f,
    "retry" => true,
    "unique_digest" => "praja_unique_jobs:2b6ce0ce8a9b151ac8027bc7238e6315:RUN"  # From your log
  }
  
  # Manually create the orphaned job state in Redis (simulating what would happen with the fix)
  Sidekiq::ProcessingTracker.redis_sync do |conn|
    job_tracking_key = Sidekiq::ProcessingTracker.send(:namespaced_key, "jobs:#{dead_instance_id}")
    job_data_key = Sidekiq::ProcessingTracker.send(:namespaced_key, "job:#{job_id}")
    
    conn.sadd(job_tracking_key, job_id)
    conn.set(job_data_key, job_data.to_json)
    
    puts "✅ Created orphaned job state (simulating fixed shutdown behavior):"
    puts "   Instance: #{dead_instance_id}"
    puts "   Job ID: #{job_id}"
    puts "   Worker: IdentityPhotoGenerationWorker"
    puts "   Unique digest: #{job_data['unique_digest']}"
  end
  
  puts
  
  # Step 2: Check Redis state
  puts "Step 2: Redis state with orphaned job..."
  system("ruby debug_redis_state.rb")
  puts
  
  # Step 3: Simulate new container startup with orphan recovery
  puts "Step 3: Simulating new container startup (like your restart)..."
  
  # Configure ProcessingTracker for the new instance
  new_instance_id = "394469022c22e563"  # From your restart log
  Sidekiq::ProcessingTracker.configure do |config|
    config.instance_id = new_instance_id
    config.namespace = "sidekiq_processing"
    config.heartbeat_interval = 30
    config.heartbeat_ttl = 90
    config.recovery_lock_ttl = 300
  end
  
  # Send heartbeat for new instance
  Sidekiq::ProcessingTracker.send(:send_heartbeat)
  puts "New instance started: #{new_instance_id}"
  
  # Run orphan recovery (this is what should find your job)
  puts "Running orphan recovery..."
  begin
    Sidekiq::ProcessingTracker.reenqueue_orphans!
    puts "✅ Orphan recovery completed"
  rescue => e
    puts "❌ Orphan recovery failed: #{e.message}"
    puts e.backtrace.join("\n")
  end
  
  puts
  
  # Step 4: Check final state
  puts "Step 4: Final state after recovery..."
  system("ruby debug_redis_state.rb")
  puts
  
  # Step 5: Check if job was re-enqueued
  puts "Step 5: Checking if IdentityPhotoGenerationWorker was re-enqueued..."
  Sidekiq.redis do |conn|
    default_queue_size = conn.llen("queue:default")
    puts "Default queue size: #{default_queue_size}"
    
    if default_queue_size > 0
      puts "✅ Job was successfully re-enqueued!"
      
      # Look for our specific job
      queue_jobs = conn.lrange("queue:default", 0, -1)
      queue_jobs.each_with_index do |job_json, index|
        job = JSON.parse(job_json)
        if job['class'] == 'IdentityPhotoGenerationWorker'
          puts "Found IdentityPhotoGenerationWorker at position #{index}:"
          puts "  JID: #{job['jid']}"
          puts "  Args: #{job['args']}"
          puts "  Created: #{Time.at(job['created_at'] || 0)}"
        end
      end
    else
      puts "❌ No jobs found in default queue"
    end
  end
  
  puts
  puts "=== Scenario Test Completed ==="
  puts
  puts "🎯 Summary:"
  puts "- Your IdentityPhotoGenerationWorker job would now be recovered"
  puts "- The fix prevents shutdown cleanup of running jobs"
  puts "- Orphan recovery detects and re-enqueues lost jobs on restart"
end

# Run the simulation
if __FILE__ == $0
  simulate_your_scenario
end
