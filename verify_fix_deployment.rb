#!/usr/bin/env ruby

# Script to verify if the shutdown hook fix is deployed
puts "=== Verifying ProcessingTracker Fix Deployment ==="
puts

# Check if we can load the gem
begin
  require_relative 'lib/sidekiq-processing-tracker'
  puts "✅ ProcessingTracker gem loaded successfully"
rescue LoadError => e
  puts "❌ Failed to load ProcessingTracker gem: #{e.message}"
  exit 1
end

# Check the version
version = Sidekiq::ProcessingTracker::VERSION rescue "unknown"
puts "📦 Version: #{version}"

# Check if the fix is present by examining the shutdown hook code
main_file = File.join(__dir__, 'lib', 'sidekiq-processing-tracker.rb')
if File.exist?(main_file)
  content = File.read(main_file)
  
  # Look for the fixed shutdown hook
  if content.include?("leaving \#{tracked_jobs.size} tracked jobs for orphan recovery")
    puts "✅ Shutdown hook fix is present in local code"
  else
    puts "❌ Shutdown hook fix NOT found in local code"
  end

  # Look for the problematic old code
  if content.include?("cleaning up \#{tracked_jobs.size} tracked jobs on shutdown")
    puts "❌ Old problematic shutdown hook code still present"
  else
    puts "✅ Old problematic shutdown hook code removed"
  end
else
  puts "⚠️  Cannot verify local code (file not found)"
end

puts
puts "=== Deployment Verification ==="
puts

# Instructions for verifying deployment
puts "To verify the fix is deployed in your application:"
puts
puts "1. Check your Gemfile references the correct commit:"
puts "   gem 'sidekiq-processing-tracker',"
puts "       git: 'https://github.com/ItsManikantaGopi/sidekiq-processing-tracker.git',"
puts "       branch: 'main',"
puts "       ref: '2eb9075220bd65549fe722a4f912a39b7d3a445f'"
puts
puts "2. Run bundle update to fetch the latest code:"
puts "   bundle update sidekiq-processing-tracker"
puts
puts "3. Redeploy your application/container"
puts
puts "4. Look for these log messages during shutdown:"
puts "   ✅ 'ProcessingTracker leaving X tracked jobs for orphan recovery'"
puts "   ❌ 'ProcessingTracker cleaning up X tracked jobs on shutdown' (old behavior)"
puts
puts "5. Look for these log messages during startup:"
puts "   ✅ 'ProcessingTracker found X orphaned jobs, re-enqueuing'"
puts

puts "=== Current Git Status ==="
puts
system("git log --oneline -3")
puts
puts "Current commit: #{`git rev-parse HEAD`.strip}"
puts "Expected commit: 2eb9075220bd65549fe722a4f912a39b7d3a445f"

if `git rev-parse HEAD`.strip == "2eb9075220bd65549fe722a4f912a39b7d3a445f"
  puts "✅ You're on the correct commit with the fix"
else
  puts "⚠️  You're on a different commit - make sure to push and update your ref"
end
